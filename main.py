import sys
import os
import warnings
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt, qInstallMessageHandler, QtMsgType, QTimer
from PyQt5.QtGui import QFont

# إعداد إخفاء التحذيرات الشامل
warnings.filterwarnings("ignore")
os.environ['PYTHONWARNINGS'] = 'ignore'
os.environ['QT_LOGGING_RULES'] = '*.debug=false;*.info=false;*.warning=false'

# إخفاء رسائل CSS غير المدعومة نهائياً
def qt_message_handler(mode, _, message):
    """مرشح رسائل Qt لإخفاء رسائل CSS غير المدعومة"""
    # قائمة الرسائل المراد إخفاؤها
    blocked_messages = [
        'Unknown property',
        'text-shadow',
        'box-shadow',
        'transform',
        'transition',
        'filter',
        'backdrop-filter',
        'overflow',
        'text-overflow',
        'cursor',
        'letter-spacing',
        'word-spacing',
        'text-decoration',
        'outline',
        'resize',
        'user-select',
        'pointer-events',
        'clip-path',
        'mask',
        'opacity',
        'visibility',
        'z-index',
        'position',
        'top',
        'left',
        'right',
        'bottom',
        'float',
        'clear',
        'display',
        'flex',
        'grid',
        'animation',
        'keyframes'
    ]

    # إخفاء الرسائل المحددة
    if any(blocked in message for blocked in blocked_messages):
        return

    # السماح بالرسائل الحرجة فقط
    if mode in [QtMsgType.QtCriticalMsg, QtMsgType.QtFatalMsg]:
        print(f"Qt Critical: {message}")

# تطبيق مرشح الرسائل
qInstallMessageHandler(qt_message_handler)

from ui.main_window import MainWindow
from ui.login_screen import LoginScreen
from database import init_db

def setup_application():
    """إعداد تطبيق PyQt"""
    # تعيين خيارات الأداء العالي قبل إنشاء التطبيق
    QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    QApplication.setAttribute(Qt.AA_DontCreateNativeWidgetSiblings, True)

    # إنشاء تطبيق PyQt
    app = QApplication(sys.argv)

    # تعيين اسم التطبيق
    app.setApplicationName("برنامج المحاسبة الإداري")

    # تعيين نمط التطبيق
    app.setStyle("Fusion")

    # تعيين اتجاه النص من اليمين إلى اليسار للغة العربية
    app.setLayoutDirection(Qt.RightToLeft)

    # تعيين الخط الافتراضي
    try:
        font = QFont()
        font.setFamily("Arial")
        font.setPointSize(10)
        app.setFont(font)
    except:
        pass  # تجاهل أخطاء الخط

    return app



def show_main_window(session, user):
    """عرض النافذة الرئيسية بعد نجاح تسجيل الدخول"""
    try:
        print("🚀 إنشاء النافذة الرئيسية...")

        # التحقق من صحة المعاملات مع تفاصيل أكثر
        if not session:
            print(f"❌ الجلسة المستلمة: {session}")
            raise ValueError("جلسة قاعدة البيانات غير صحيحة")
        if not user:
            print(f"❌ المستخدم المستلم: {user}")
            raise ValueError("بيانات المستخدم غير صحيحة")

        # التحقق من صحة الجلسة
        try:
            session.execute("SELECT 1")
            print("✅ الجلسة صحيحة وقابلة للاستخدام")
        except Exception as session_error:
            print(f"⚠️ مشكلة في الجلسة: {session_error}")
            # محاولة إنشاء جلسة جديدة
            from database import get_session
            session = get_session()
            print("✅ تم إنشاء جلسة جديدة")

        print(f"✅ تم استلام الجلسة والمستخدم بنجاح: {user.full_name}")

        # إنشاء النافذة الرئيسية مع المعاملات الصحيحة
        window = MainWindow(session=session, current_user=user)
        print("✅ تم إنشاء النافذة الرئيسية بنجاح")

        # إظهار النافذة الرئيسية فوراً
        print("📺 إظهار النافذة...")
        window.show()

        # تكبير النافذة بعد إظهارها بوقت كافي
        QTimer.singleShot(200, window.showMaximized)

        print("🎉 تم تشغيل البرنامج بنجاح!")
        return window

    except Exception as e:
        error_message = f"❌ حدث خطأ في إنشاء النافذة الرئيسية: {str(e)}"
        print(error_message)
        import traceback
        traceback.print_exc()

        # محاولة عرض رسالة خطأ للمستخدم
        try:
            QMessageBox.critical(None, "خطأ في النظام",
                               f"فشل في تشغيل البرنامج:\n{str(e)}\n\nيرجى إعادة تشغيل البرنامج.")
        except:
            pass

        return None


def main():
    """الدالة الرئيسية للتطبيق مع شاشة دخول متقدمة"""
    try:
        print("🚀 بدء تشغيل البرنامج...")

        # إعداد تطبيق PyQt
        app = setup_application()
        print("✅ تم إعداد التطبيق بنجاح")

        # إعداد قاعدة البيانات مع التحسينات المتقدمة
        print("🔧 إعداد قاعدة البيانات مع التحسينات المتقدمة...")
        init_db()
        print("✅ تم إعداد قاعدة البيانات مع التحسينات بنجاح")

        # التحقق من وجود المستخدم الإداري (سيتم إنشاؤه تلقائياً في init_db)
        # لا نحتاج لإنشاء مستخدم هنا لأن init_db تتولى ذلك بالطريقة الصحيحة

        # إنشاء وعرض شاشة الدخول المتقدمة
        print("� إنشاء شاشة الدخول المتقدمة...")
        login_screen = LoginScreen()

        # متغير لحفظ النافذة الرئيسية
        main_window = None

        def on_login_success(session, user):
            """معالجة نجاح تسجيل الدخول"""
            nonlocal main_window
            main_window = show_main_window(session, user)

        # ربط إشارة نجاح تسجيل الدخول
        login_screen.login_successful.connect(on_login_success)

        # عرض شاشة الدخول
        login_screen.show()
        print("✅ تم عرض شاشة الدخول المتقدمة بنجاح")

        # تشغيل حلقة الأحداث
        sys.exit(app.exec_())

    except Exception as e:
        # معالجة الأخطاء غير المتوقعة
        error_message = f"❌ حدث خطأ غير متوقع: {str(e)}"
        print(error_message)

        # طباعة تفاصيل الخطأ للمطور
        import traceback
        traceback.print_exc()

        # محاولة عرض رسالة الخطأ في نافذة منبثقة
        try:
            QMessageBox.critical(None, "خطأ في البرنامج", error_message)
        except:
            # إذا فشل عرض النافذة المنبثقة، اطبع الخطأ في وحدة التحكم
            print("❌ فشل في عرض نافذة الخطأ")

        sys.exit(1)

if __name__ == "__main__":
    main()
    